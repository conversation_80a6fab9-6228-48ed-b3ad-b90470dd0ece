import React from 'react';
import { LocalStorageStateProvider, useLocalStorageState } from '../lib/use-local-storage-state';

// 用户信息组件
function UserProfile() {
    const [user, setUser] = useLocalStorageState('user', { name: '', email: '' });

    return (
        <div>
            <h3>用户信息</h3>
            <input
                type="text"
                placeholder="姓名"
                value={user.name}
                onChange={(e) => setUser({ ...user, name: e.target.value })}
            />
            <input
                type="email"
                placeholder="邮箱"
                value={user.email}
                onChange={(e) => setUser({ ...user, email: e.target.value })}
            />
        </div>
    );
}

// 用户显示组件
function UserDisplay() {
    const [user] = useLocalStorageState('user', { name: '', email: '' });

    return (
        <div>
            <h3>当前用户</h3>
            <p>姓名: {user.name || '未设置'}</p>
            <p>邮箱: {user.email || '未设置'}</p>
        </div>
    );
}

// 主题设置组件
function ThemeSettings() {
    const [theme, setTheme] = useLocalStorageState('theme', 'light');

    return (
        <div>
            <h3>主题设置</h3>
            <label>
                <input
                    type="radio"
                    value="light"
                    checked={theme === 'light'}
                    onChange={(e) => setTheme(e.target.value)}
                />
                浅色主题
            </label>
            <label>
                <input
                    type="radio"
                    value="dark"
                    checked={theme === 'dark'}
                    onChange={(e) => setTheme(e.target.value)}
                />
                深色主题
            </label>
        </div>
    );
}

// 主题显示组件
function ThemeDisplay() {
    const [theme] = useLocalStorageState('theme', 'light');

    return (
        <div style={{ 
            backgroundColor: theme === 'dark' ? '#333' : '#fff',
            color: theme === 'dark' ? '#fff' : '#333',
            padding: '10px'
        }}>
            <h3>当前主题: {theme}</h3>
        </div>
    );
}

// 示例应用
export function LocalStorageStateExample() {
    return (
        <LocalStorageStateProvider>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
                <div>
                    <UserProfile />
                    <ThemeSettings />
                </div>
                <div>
                    <UserDisplay />
                    <ThemeDisplay />
                </div>
            </div>
        </LocalStorageStateProvider>
    );
}
